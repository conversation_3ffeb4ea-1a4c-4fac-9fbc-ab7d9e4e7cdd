import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../constants/app_theme.dart';
import '../../models/booking_model.dart';
import '../../models/trip_model.dart';
import '../../models/conversation_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/trip_provider.dart';
import '../../services/booking_service.dart';
import '../../services/message_service.dart';
import '../../services/notification_service.dart';
import '../chat/chat_page.dart';
import '../../widgets/trip_card.dart';
import '../booking/booking_request_card.dart';

class DriverDashboard extends StatefulWidget {
  const DriverDashboard({super.key});

  @override
  State<DriverDashboard> createState() => _DriverDashboardState();
}

class _DriverDashboardState extends State<DriverDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  
  List<BookingModel> _pendingBookings = [];
  List<BookingModel> _acceptedBookings = [];
  List<ConversationModel> _conversations = [];
  List<TripModel> _completedTrips = [];
  
  bool _isLoading = true;
  int _pendingRequestsCount = 0;
  int _unreadMessagesCount = 0;
  
  RealtimeChannel? _bookingChannel;
  RealtimeChannel? _messageChannel;
  RealtimeChannel? _notificationChannel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDashboardData();
    _setupRealtimeSubscriptions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cleanupSubscriptions();
    super.dispose();
  }

  Future<void> _loadDashboardData() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      if (kDebugMode) {
        print('❌ No current user found for dashboard');
      }
      return;
    }

    if (kDebugMode) {
      print('🔄 Loading dashboard data for driver: ${currentUser.id}');
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Load pending bookings
      if (kDebugMode) {
        print('📋 Loading pending bookings...');
      }
      final pendingBookings = await BookingService.getPendingBookings(currentUser.id);
      if (kDebugMode) {
        print('✅ Loaded ${pendingBookings.length} pending bookings');
      }

      // Load all driver bookings
      if (kDebugMode) {
        print('📋 Loading all driver bookings...');
      }
      final allBookings = await BookingService.getDriverBookings(currentUser.id);
      final accepted = allBookings.where((b) => b.status == 'accepted').toList();
      if (kDebugMode) {
        print('✅ Loaded ${allBookings.length} total bookings, ${accepted.length} accepted');
      }

      // Load conversations
      if (kDebugMode) {
        print('💬 Loading conversations...');
      }
      final conversations = await MessageService.getUserConversations(currentUser.id);
      if (kDebugMode) {
        print('✅ Loaded ${conversations.length} conversations');
      }

      // Load completed trips
      if (mounted) {
        if (kDebugMode) {
          print('🚗 Loading completed trips...');
        }
        final tripProvider = Provider.of<TripProvider>(context, listen: false);
        await tripProvider.loadUserTrips(currentUser.id);
        final completedTrips = tripProvider.userTrips
            .where((trip) => trip.status == 'completed')
            .toList();
        if (kDebugMode) {
          print('✅ Loaded ${completedTrips.length} completed trips');
        }

        setState(() {
          _pendingBookings = pendingBookings;
          _acceptedBookings = accepted;
          _conversations = conversations;
          _completedTrips = completedTrips;
          _pendingRequestsCount = pendingBookings.length;
          _unreadMessagesCount = conversations
              .map((c) => c.getUnreadCount(currentUser.id))
              .fold(0, (sum, count) => sum + count);
          _isLoading = false;
        });

        if (kDebugMode) {
          print('🎉 Dashboard data loaded successfully');
          print('   - Pending requests: $_pendingRequestsCount');
          print('   - Accepted bookings: ${_acceptedBookings.length}');
          print('   - Conversations: ${_conversations.length}');
          print('   - Completed trips: ${_completedTrips.length}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading dashboard data: $e');
        print('Error type: ${e.runtimeType}');
        print('Stack trace: ${StackTrace.current}');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  void _setupRealtimeSubscriptions() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;
    
    if (currentUser == null) return;

    // Subscribe to booking updates
    _bookingChannel = Supabase.instance.client
        .channel('driver_bookings:${currentUser.id}')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: currentUser.id,
          ),
          callback: (payload) {
            _loadDashboardData(); // Refresh data when bookings change
          },
        )
        .subscribe();

    // Subscribe to notifications
    _notificationChannel = NotificationService.subscribeToUserNotifications(
      currentUser.id,
      (notification) {
        if (notification.isBookingRequest) {
          _loadDashboardData(); // Refresh when new booking request arrives
        }
      },
    );
  }

  void _cleanupSubscriptions() {
    if (_bookingChannel != null) {
      Supabase.instance.client.removeChannel(_bookingChannel!);
    }
    if (_messageChannel != null) {
      MessageService.unsubscribeFromMessages(_messageChannel!);
    }
    if (_notificationChannel != null) {
      NotificationService.unsubscribeFromNotifications(_notificationChannel!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة تحكم السائق'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: AppColors.primary,
            child: TabBar(
              controller: _tabController,
              indicatorColor: Colors.white,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              tabs: [
                const Tab(
                  icon: Icon(Icons.directions_car),
                  text: 'الرحلات النشطة',
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.pending),
                      const SizedBox(width: 8),
                      const Text('الطلبات'),
                      if (_pendingRequestsCount > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$_pendingRequestsCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.chat),
                      const SizedBox(width: 8),
                      const Text('المحادثات'),
                      if (_unreadMessagesCount > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppColors.secondary,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$_unreadMessagesCount',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const Tab(
                  icon: Icon(Icons.check_circle),
                  text: 'المكتملة',
                ),
              ],
            ),
          ),

          // Tab Views
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _ActiveTripsTab(bookings: _acceptedBookings),
                      _PendingRequestsTab(
                        bookings: _pendingBookings,
                        onBookingUpdated: _loadDashboardData,
                      ),
                      _ChatsTab(conversations: _conversations),
                      _CompletedTripsTab(trips: _completedTrips),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
}

// Active Trips Tab
class _ActiveTripsTab extends StatelessWidget {
  final List<BookingModel> bookings;

  const _ActiveTripsTab({required this.bookings});

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.directions_car_outlined,
        title: 'لا توجد رحلات نشطة',
        subtitle: 'ستظهر الرحلات المحجوزة هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        final booking = bookings[index];
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundImage: booking.passenger?.profileImageUrl != null
                          ? NetworkImage(booking.passenger!.profileImageUrl!)
                          : null,
                      child: booking.passenger?.profileImageUrl == null
                          ? const Icon(Icons.person)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            booking.passenger?.fullName ?? 'مسافر',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            '${booking.seatsBooked} مقعد - ${booking.totalPrice.toInt()} درهم',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => ChatPage(
                              tripId: booking.tripId,
                              otherUserId: booking.passengerId,
                              conversationId: null,
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.chat),
                      color: AppColors.primary,
                    ),
                  ],
                ),
                if (booking.message != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      booking.message!,
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

// Pending Requests Tab
class _PendingRequestsTab extends StatelessWidget {
  final List<BookingModel> bookings;
  final VoidCallback? onBookingUpdated;

  const _PendingRequestsTab({
    required this.bookings,
    this.onBookingUpdated,
  });

  @override
  Widget build(BuildContext context) {
    if (bookings.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.pending_outlined,
        title: 'لا توجد طلبات معلقة',
        subtitle: 'ستظهر طلبات الحجز الجديدة هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookings.length,
      itemBuilder: (context, index) {
        return BookingRequestCard(
          booking: bookings[index],
          onBookingUpdated: onBookingUpdated,
        );
      },
    );
  }
}

// Chats Tab
class _ChatsTab extends StatelessWidget {
  final List<ConversationModel> conversations;

  const _ChatsTab({required this.conversations});

  @override
  Widget build(BuildContext context) {
    if (conversations.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.chat_outlined,
        title: 'لا توجد محادثات',
        subtitle: 'ستظهر محادثاتك مع المسافرين هنا بعد قبول الحجوزات',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        return _ConversationCard(
          conversation: conversations[index],
          onTap: () => _openChat(context, conversations[index]),
        );
      },
    );
  }

  void _openChat(BuildContext context, ConversationModel conversation) {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final currentUser = authProvider.currentUser;

    if (currentUser == null || conversation.tripId == null) return;

    // Determine the other user ID (passenger for driver)
    final otherUserId = conversation.passengerId;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(
          tripId: conversation.tripId!,
          otherUserId: otherUserId,
        ),
      ),
    );
  }
}

// Conversation Card Widget
class _ConversationCard extends StatelessWidget {
  final ConversationModel conversation;
  final VoidCallback onTap;

  const _ConversationCard({
    required this.conversation,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) return const SizedBox.shrink();

    // Get other user info
    final otherUserName = conversation.getOtherUserName(currentUser.id);
    final otherUserImage = conversation.getOtherUserImage(currentUser.id);
    final unreadCount = conversation.getUnreadCount(currentUser.id);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        onTap: onTap,
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundImage: otherUserImage != null
                  ? NetworkImage(otherUserImage)
                  : null,
              child: otherUserImage == null
                  ? const Icon(Icons.person, size: 24)
                  : null,
            ),
            // Show unread count badge if there are unread messages
            if (unreadCount > 0)
              Positioned(
                right: 0,
                top: 0,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppColors.secondary,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          otherUserName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (conversation.tripFrom != null && conversation.tripTo != null)
              Text(
                '${conversation.tripFrom} ← ${conversation.tripTo}',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            const SizedBox(height: 2),
            Text(
              conversation.lastMessage ?? 'لا توجد رسائل',
              style: TextStyle(
                color: AppColors.textTertiary,
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        trailing: conversation.lastMessageAt != null
            ? Text(
                _formatTime(conversation.lastMessageAt!),
                style: TextStyle(
                  color: AppColors.textTertiary,
                  fontSize: 11,
                ),
              )
            : null,
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}

// Completed Trips Tab
class _CompletedTripsTab extends StatelessWidget {
  final List<TripModel> trips;

  const _CompletedTripsTab({required this.trips});

  @override
  Widget build(BuildContext context) {
    if (trips.isEmpty) {
      return _buildEmptyState(
        context,
        icon: Icons.check_circle_outline,
        title: 'لا توجد رحلات مكتملة',
        subtitle: 'ستظهر رحلاتك المكتملة هنا',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: trips.length,
      itemBuilder: (context, index) {
        return TripCard(
          trip: trips[index],
          showBookButton: false,
          onTap: () {
            // Navigate to trip details
          },
        );
      },
    );
  }
}

Widget _buildEmptyState(
  BuildContext context, {
  required IconData icon,
  required String title,
  required String subtitle,
}) {
  return Center(
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: 64,
          color: AppColors.textTertiary,
        ),
        const SizedBox(height: 16),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    ),
  );
}
