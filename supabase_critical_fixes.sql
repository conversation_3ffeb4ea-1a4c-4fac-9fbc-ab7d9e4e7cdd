-- =====================================================
-- CRITICAL SUPABASE FIXES FOR SAFARNI APP
-- Fix all PostgrestException errors from terminal logs
-- =====================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. FIX CONVERSATIONS TABLE SCHEMA
-- =====================================================

-- Add missing columns to conversations table
ALTER TABLE public.conversations 
ADD COLUMN IF NOT EXISTS booking_id UUID,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS unread_count_driver INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS unread_count_passenger INTEGER DEFAULT 0;

-- Add foreign key constraint for booking_id if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'conversations_booking_id_fkey'
    ) THEN
        ALTER TABLE public.conversations 
        ADD CONSTRAINT conversations_booking_id_fkey
        FOREIGN KEY (booking_id) REFERENCES public.bookings(id) ON DELETE SET NULL;
    END IF;
END $$;

-- =====================================================
-- 2. CREATE NOTIFICATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    related_user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    trip_id UUID REFERENCES public.trips(id) ON DELETE SET NULL,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
    type TEXT NOT NULL CHECK (type IN ('booking_request', 'booking_accepted', 'booking_rejected', 'message', 'trip_update', 'payment')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. CREATE MISSING FUNCTIONS
-- =====================================================

-- Function: get_or_create_conversation (Enhanced)
CREATE OR REPLACE FUNCTION get_or_create_conversation(
    p_trip_id UUID,
    p_booking_id UUID DEFAULT NULL,
    p_driver_id UUID,
    p_passenger_id UUID
) RETURNS UUID AS $$
DECLARE
    conversation_id UUID;
BEGIN
    -- Try to find existing conversation
    SELECT id INTO conversation_id
    FROM public.conversations
    WHERE trip_id = p_trip_id
    AND passenger_id = p_passenger_id
    LIMIT 1;

    -- If not found, create new conversation
    IF conversation_id IS NULL THEN
        INSERT INTO public.conversations (
            trip_id,
            booking_id,
            driver_id,
            passenger_id,
            unread_count_driver,
            unread_count_passenger,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            p_trip_id,
            p_booking_id,
            p_driver_id,
            p_passenger_id,
            0,
            0,
            true,
            NOW(),
            NOW()
        ) RETURNING id INTO conversation_id;

        -- Log conversation creation
        RAISE NOTICE 'Created new conversation % for trip % between driver % and passenger %',
            conversation_id, p_trip_id, p_driver_id, p_passenger_id;
    ELSE
        -- Update booking_id if provided and not already set
        IF p_booking_id IS NOT NULL THEN
            UPDATE public.conversations
            SET booking_id = p_booking_id,
                updated_at = NOW()
            WHERE id = conversation_id
            AND booking_id IS NULL;

            RAISE NOTICE 'Updated conversation % with booking_id %', conversation_id, p_booking_id;
        END IF;
    END IF;

    RETURN conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function: create_conversation_on_booking_acceptance (Trigger Function)
CREATE OR REPLACE FUNCTION create_conversation_on_booking_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create conversation when booking status changes to 'accepted'
    IF NEW.status = 'accepted' AND (OLD.status IS NULL OR OLD.status != 'accepted') THEN
        -- Create or get conversation using our function
        PERFORM get_or_create_conversation(
            NEW.trip_id,
            NEW.id,
            NEW.driver_id,
            NEW.passenger_id
        );

        RAISE NOTICE 'Conversation created/updated for accepted booking %', NEW.id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic conversation creation
DROP TRIGGER IF EXISTS trigger_create_conversation_on_booking_acceptance ON public.bookings;
CREATE TRIGGER trigger_create_conversation_on_booking_acceptance
    AFTER UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION create_conversation_on_booking_acceptance();

-- Function: get_driver_booking_counts (for dashboard)
CREATE OR REPLACE FUNCTION get_driver_booking_counts(p_driver_id UUID)
RETURNS TABLE(
    pending_count INTEGER,
    accepted_count INTEGER,
    completed_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(CASE WHEN b.status = 'pending' THEN 1 END)::INTEGER as pending_count,
        COUNT(CASE WHEN b.status = 'accepted' THEN 1 END)::INTEGER as accepted_count,
        COUNT(CASE WHEN b.status = 'completed' THEN 1 END)::INTEGER as completed_count
    FROM public.bookings b
    WHERE b.driver_id = p_driver_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function: get_passenger_bookings
CREATE OR REPLACE FUNCTION get_passenger_bookings(
    p_passenger_id UUID,
    p_status TEXT DEFAULT NULL
) RETURNS TABLE (
    id UUID,
    trip_id UUID,
    passenger_id UUID,
    driver_id UUID,
    seats_booked INTEGER,
    total_price DECIMAL,
    status TEXT,
    booking_type TEXT,
    message TEXT,
    special_requests TEXT,
    passenger_details JSONB,
    has_luggage BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    trip_data JSONB,
    driver_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.trip_id,
        b.passenger_id,
        b.driver_id,
        b.seats_booked,
        b.total_price,
        b.status,
        b.booking_type,
        b.message,
        b.special_requests,
        b.passenger_details,
        COALESCE((b.passenger_details->>'has_luggage')::boolean, false) as has_luggage,
        b.created_at,
        b.confirmed_at,
        to_jsonb(t.*) as trip_data,
        to_jsonb(u.*) as driver_data
    FROM public.bookings b
    JOIN public.trips t ON b.trip_id = t.id
    JOIN public.users u ON b.driver_id = u.id
    WHERE b.passenger_id = p_passenger_id
    AND (p_status IS NULL OR b.status = p_status)
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(user_id, is_read);
CREATE INDEX IF NOT EXISTS idx_conversations_trip_passenger ON public.conversations(trip_id, passenger_id);
CREATE INDEX IF NOT EXISTS idx_conversations_is_active ON public.conversations(is_active);
CREATE INDEX IF NOT EXISTS idx_bookings_driver_status ON public.bookings(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_bookings_passenger_status ON public.bookings(passenger_id, status);

-- =====================================================
-- 5. SETUP ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Notifications policies
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can insert notifications" ON public.notifications;
CREATE POLICY "Users can insert notifications" ON public.notifications
    FOR INSERT WITH CHECK (true); -- Allow any authenticated user to create notifications

DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (user_id = auth.uid());

-- Conversations policies
DROP POLICY IF EXISTS "Users can view their own conversations" ON public.conversations;
CREATE POLICY "Users can view their own conversations" ON public.conversations
    FOR SELECT USING (
        driver_id = auth.uid() OR 
        passenger_id = auth.uid()
    );

DROP POLICY IF EXISTS "Users can insert conversations" ON public.conversations;
CREATE POLICY "Users can insert conversations" ON public.conversations
    FOR INSERT WITH CHECK (
        driver_id = auth.uid() OR 
        passenger_id = auth.uid()
    );

DROP POLICY IF EXISTS "Users can update their own conversations" ON public.conversations;
CREATE POLICY "Users can update their own conversations" ON public.conversations
    FOR UPDATE USING (
        driver_id = auth.uid() OR 
        passenger_id = auth.uid()
    );

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

-- Grant function permissions
GRANT EXECUTE ON FUNCTION get_or_create_conversation TO authenticated;
GRANT EXECUTE ON FUNCTION get_passenger_bookings TO authenticated;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.conversations TO authenticated;

-- =====================================================
-- 7. CREATE TRIGGERS FOR AUTOMATIC CONVERSATION CREATION
-- =====================================================

-- Function to create conversation when booking is accepted
CREATE OR REPLACE FUNCTION create_conversation_on_booking_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create conversation when booking status changes to 'accepted'
    IF NEW.status = 'accepted' AND (OLD.status IS NULL OR OLD.status != 'accepted') THEN
        PERFORM get_or_create_conversation(
            NEW.trip_id,
            NEW.id,
            NEW.driver_id,
            NEW.passenger_id
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic conversation creation
DROP TRIGGER IF EXISTS trigger_create_conversation_on_booking_acceptance ON public.bookings;
CREATE TRIGGER trigger_create_conversation_on_booking_acceptance
    AFTER UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION create_conversation_on_booking_acceptance();

-- =====================================================
-- 8. FINAL VERIFICATION
-- =====================================================

-- Test the functions exist
DO $$
BEGIN
    -- Test get_or_create_conversation function
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'get_or_create_conversation'
    ) THEN
        RAISE EXCEPTION 'Function get_or_create_conversation was not created successfully';
    END IF;
    
    -- Test get_passenger_bookings function
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'get_passenger_bookings'
    ) THEN
        RAISE EXCEPTION 'Function get_passenger_bookings was not created successfully';
    END IF;
    
    RAISE NOTICE 'All critical database fixes applied successfully!';
END $$;
