import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notification_model.dart';

/// Web-compatible notification service stub
class NotificationService {
  static final SupabaseClient _client = Supabase.instance.client;
  static bool _isInitialized = false;

  static Future<void> initialize() async {
    if (_isInitialized) return;
    if (kDebugMode) {
      print('🔔 NotificationService initialized (web-compatible mode)');
    }
    _isInitialized = true;
  }

  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (kDebugMode) {
      print('🔔 Notification: $title - $body');
    }
  }

  static Future<void> showBookingNotification({
    required String title,
    required String message,
    String? bookingId,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: message,
      payload: bookingId,
    );
  }

  static Future<void> showTripNotification({
    required String title,
    required String message,
    String? tripId,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: message,
      payload: tripId,
    );
  }

  static Future<void> showMessageNotification({
    required String senderName,
    required String message,
    String? conversationId,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'رسالة جديدة من $senderName',
      body: message,
      payload: conversationId,
    );
  }

  static Future<void> cancelNotification(int id) async {
    if (kDebugMode) {
      print('🔔 Cancelled notification: $id');
    }
  }

  static Future<void> cancelAllNotifications() async {
    if (kDebugMode) {
      print('🔔 Cancelled all notifications');
    }
  }

  static Future<List<dynamic>> getPendingNotifications() async {
    return [];
  }

  static Future<void> createNotification({
    required String userId,
    required String title,
    required String message,
    String? type,
    Map<String, dynamic>? data,
  }) async {
    try {
      await _client.from('notifications').insert({
        'user_id': userId,
        'title': title,
        'message': message,
        'type': type ?? 'general',
        'data': data,
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      if (kDebugMode) {
        print('✅ Database notification created: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
    }
  }

  static Future<List<NotificationModel>> getUserNotifications(String userId, {int? limit}) async {
    try {
      final query = _client
          .from('notifications')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      if (limit != null) {
        query.limit(limit);
      } else {
        query.limit(50); // Default limit
      }

      final response = await query;

      return (response as List)
          .map((json) => NotificationModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching notifications: $e');
      }
      return [];
    }
  }

  static Future<void> markAsRead(String notificationId) async {
    try {
      await _client
          .from('notifications')
          .update({'is_read': true})
          .eq('id', notificationId);

      if (kDebugMode) {
        print('✅ Notification marked as read: $notificationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
    }
  }

  static Future<void> deleteNotification(String notificationId) async {
    try {
      await _client
          .from('notifications')
          .delete()
          .eq('id', notificationId);

      if (kDebugMode) {
        print('✅ Notification deleted: $notificationId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
    }
  }

  static Future<int> getUnreadCount(String userId) async {
    try {
      final response = await _client
          .from('notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('is_read', false);

      return (response as List).length;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unread count: $e');
      }
      return 0;
    }
  }

  // Additional methods that might be called by other parts of the app
  static Future<void> markAllAsRead(String userId) async {
    try {
      await _client
          .from('notifications')
          .update({'is_read': true})
          .eq('user_id', userId)
          .eq('is_read', false);

      if (kDebugMode) {
        print('✅ All notifications marked as read for user: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking all notifications as read: $e');
      }
    }
  }

  static subscribeToUserNotifications(String userId, [Function(NotificationModel)? callback]) {
    // Return a mock subscription for web compatibility
    if (kDebugMode) {
      print('🔔 Subscribed to notifications for user: $userId (web-compatible mode)');
      if (callback != null) {
        print('🔔 Callback provided for notification updates');
      }
    }
    return null;
  }

  static void unsubscribeFromNotifications(dynamic subscription) {
    if (kDebugMode) {
      print('🔔 Unsubscribed from notifications (web-compatible mode)');
    }
  }

  static Future<void> sendNotification({
    required String userId,
    required String title,
    required String message,
    String? type,
    String? notificationType,
    String? tripId,
    String? bookingId,
    String? relatedUserId,
    Map<String, dynamic>? data,
  }) async {
    await createNotification(
      userId: userId,
      title: title,
      message: message,
      type: type ?? notificationType,
      data: data,
    );
  }

  // Missing method: showLocalNotification
  static Future<void> showLocalNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      id: id,
      title: title,
      body: body,
      payload: payload,
    );
  }

  // Missing method: showBookingRequestNotification
  static Future<void> showBookingRequestNotification({
    required String driverName,
    required String passengerName,
    required String tripRoute,
    required String bookingId,
  }) async {
    await showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'طلب حجز جديد',
      body: 'طلب حجز جديد من $passengerName لرحلة $tripRoute',
      payload: bookingId,
    );
  }

  // Missing method: sendBookingRequestNotification
  static Future<void> sendBookingRequestNotification({
    required String driverId,
    required String passengerId,
    required String bookingId,
    required String tripRoute,
    required String passengerName,
  }) async {
    await createNotification(
      userId: driverId,
      title: 'طلب حجز جديد',
      message: 'طلب حجز جديد من $passengerName لرحلة $tripRoute',
      type: 'booking_request',
      data: {
        'booking_id': bookingId,
        'passenger_id': passengerId,
        'trip_route': tripRoute,
        'passenger_name': passengerName,
      },
    );
  }

  // Missing method: sendBookingAcceptanceNotification
  static Future<void> sendBookingAcceptanceNotification({
    required String passengerId,
    required String driverId,
    required String bookingId,
    required String tripRoute,
    required String driverName,
  }) async {
    await createNotification(
      userId: passengerId,
      title: 'تم قبول طلب الحجز',
      message: 'تم قبول طلب حجزك من قبل $driverName لرحلة $tripRoute',
      type: 'booking_accepted',
      data: {
        'booking_id': bookingId,
        'driver_id': driverId,
        'trip_route': tripRoute,
        'driver_name': driverName,
      },
    );
  }

  // Missing method: sendBookingRejectionNotification
  static Future<void> sendBookingRejectionNotification({
    required String passengerId,
    required String driverId,
    required String bookingId,
    required String tripRoute,
    required String driverName,
    String? rejectionReason,
  }) async {
    String message = 'تم رفض طلب حجزك من قبل $driverName لرحلة $tripRoute';
    if (rejectionReason != null && rejectionReason.isNotEmpty) {
      message += '\nالسبب: $rejectionReason';
    }

    await createNotification(
      userId: passengerId,
      title: 'تم رفض طلب الحجز',
      message: message,
      type: 'booking_rejected',
      data: {
        'booking_id': bookingId,
        'driver_id': driverId,
        'trip_route': tripRoute,
        'driver_name': driverName,
        'rejection_reason': rejectionReason,
      },
    );
  }
}
